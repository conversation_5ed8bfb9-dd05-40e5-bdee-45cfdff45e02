# CasparCG Controller

A modern web-based controller for CasparCG Server with a React frontend and Node.js backend.

## Features

- **Real-time Media Management**: Browse and manage media files from CasparCG Server
- **Dual Channel Control**: Control two CasparCG channels simultaneously
- **Playlist Management**: Create and manage playlists with drag-and-drop functionality
- **Live Thumbnails**: Preview media files with thumbnail support
- **Loop Control**: Enable/disable looping for individual channels
- **Responsive UI**: Modern, responsive interface built with React and KOR UI components

## Architecture

- **Frontend**: React 19 with Vite, TailwindCSS, and KOR UI components
- **Backend**: Node.js with Express and CasparCG Connection library
- **Communication**: RESTful API between frontend and backend

## Prerequisites

- Node.js 18+ and npm
- CasparCG Server 2.3+ running and accessible
- CasparCG Media Server (for thumbnail support)

## Installation

### 1. Clone the repository
```bash
git clone <repository-url>
cd casparcg-controller
```

### 2. Install server dependencies
```bash
cd server
npm install
```

### 3. Install client dependencies
```bash
cd ../client
npm install
```

### 4. Configure environment variables

#### Server Configuration
Copy `server/.env.example` to `server/.env` and update the values:

```bash
cd ../server
cp .env.example .env
```

Edit `.env` with your CasparCG server details:
```env
# CasparCG Server Configuration
CASPARCG_HOST=************
CASPARCG_PORT=5250

# CasparCG Media Server Configuration (for thumbnails)
CASPARCG_MEDIA_HOST=************
CASPARCG_MEDIA_PORT=8000
```

#### Client Configuration
Copy `client/.env.example` to `client/.env` and update if needed:

```bash
cd ../client
cp .env.example .env
```

## Development

### Start the development servers

#### Terminal 1 - Backend Server
```bash
cd server
npm run dev
```

#### Terminal 2 - Frontend Client
```bash
cd client
npm run dev
```

The application will be available at:
- Frontend: http://localhost:5173
- Backend API: http://localhost:3001

## Production

### Build the client
```bash
cd client
npm run build
```

### Start the production server
```bash
cd server
npm start
```

## Configuration

### Channel Configuration
The application supports configurable channel and layer settings through the `/config` page or by editing `server/channel-config.json`:

```json
{
  "channel1": {
    "channel": 1,
    "layer": 10
  },
  "channel2": {
    "channel": 2,
    "layer": 10
  }
}
```

## API Endpoints

- `GET /api/health` - Health check
- `GET /api/media` - Get media list from CasparCG
- `POST /api/play` - Play media on channel/layer
- `POST /api/pause` - Pause playback
- `POST /api/stop` - Stop playback
- `POST /api/clear` - Clear channel/layer
- `POST /api/info` - Get channel/layer info
- `GET /api/config` - Get channel configuration
- `POST /api/config` - Update channel configuration

## Troubleshooting

### Common Issues

1. **Cannot connect to CasparCG Server**
   - Verify CasparCG Server is running
   - Check the host/port configuration in `.env`
   - Ensure firewall allows connections

2. **Thumbnails not loading**
   - Verify CasparCG Media Server is running
   - Check media server host/port configuration
   - Ensure media files exist in CasparCG media folder

3. **Media list is empty**
   - Verify media files are in the CasparCG media directory
   - Check CasparCG Server logs for errors
   - Ensure proper file permissions

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

ISC License
