#!/bin/bash

# CasparCG Controller Development Setup Script

echo "🚀 Setting up CasparCG Controller for development..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

echo "✅ Node.js version: $(node --version)"

# Setup server
echo "📦 Installing server dependencies..."
cd server
if [ ! -f ".env" ]; then
    echo "📝 Creating server .env file..."
    cp .env.example .env
    echo "⚠️  Please edit server/.env with your CasparCG server details"
fi
npm install

# Setup client
echo "📦 Installing client dependencies..."
cd ../client
if [ ! -f ".env" ]; then
    echo "📝 Creating client .env file..."
    cp .env.example .env
fi
npm install

echo "✅ Setup complete!"
echo ""
echo "🎯 Next steps:"
echo "1. Edit server/.env with your CasparCG server configuration"
echo "2. Start the development servers:"
echo "   Terminal 1: cd server && npm run dev"
echo "   Terminal 2: cd client && npm run dev"
echo "3. Open http://localhost:5173 in your browser"
echo ""
echo "📚 See README.md for more information"
