import { useState, useCallback, useMemo } from 'react';
import { toast } from 'react-toastify';
import { API_CONFIG } from '../config/constants';

// Custom hook for API calls
export const useApi = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const apiCall = useCallback(async (endpoint, options = {}) => {
    setLoading(true);
    setError(null);

    try {
      const url = `${API_CONFIG.BASE_URL}/${endpoint}`;
      const config = {
        headers: {
          'Content-Type': 'application/json',
        },
        ...options,
      };

      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `HTTP error! status: ${response.status}`);
      }

      // Show success message if provided
      if (data.message && options.showSuccessToast !== false) {
        toast.success(data.message);
      }

      return data;
    } catch (err) {
      const errorMessage = err.message || 'An error occurred';
      setError(errorMessage);

      if (options.showErrorToast !== false) {
        toast.error(errorMessage);
      }

      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Convenience methods for common HTTP methods
  const get = useCallback((endpoint, options = {}) => {
    return apiCall(endpoint, { method: 'GET', ...options });
  }, [apiCall]);

  const post = useCallback((endpoint, data, options = {}) => {
    return apiCall(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
      ...options,
    });
  }, [apiCall]);

  const put = useCallback((endpoint, data, options = {}) => {
    return apiCall(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
      ...options,
    });
  }, [apiCall]);

  const del = useCallback((endpoint, options = {}) => {
    return apiCall(endpoint, { method: 'DELETE', ...options });
  }, [apiCall]);

  // Memoize the return object to prevent unnecessary re-renders
  return useMemo(() => ({
    loading,
    error,
    get,
    post,
    put,
    delete: del,
    apiCall,
  }), [loading, error, get, post, put, del, apiCall]);
};

// Custom hook for CasparCG operations
export const useCasparCG = () => {
  const api = useApi();

  const play = useCallback(async (channel, layer, clip, params) => {
    return api.post('play', { channel, layer, clip, params });
  }, [api.post]);

  const pause = useCallback(async (channel, layer) => {
    return api.post('pause', { channel, layer });
  }, [api.post]);

  const stop = useCallback(async (channel, layer) => {
    return api.post('stop', { channel, layer });
  }, [api.post]);

  const clear = useCallback(async (channel, layer) => {
    return api.post('clear', { channel, layer });
  }, [api.post]);

  const info = useCallback(async (channel, layer) => {
    return api.post('info', { channel, layer });
  }, [api.post]);

  const getMedia = useCallback(async () => {
    const response = await api.get('media', { showSuccessToast: false });
    return response.data || [];
  }, [api.get]);

  return useMemo(() => ({
    ...api,
    play,
    pause,
    stop,
    clear,
    info,
    getMedia,
  }), [api, play, pause, stop, clear, info, getMedia]);
};
