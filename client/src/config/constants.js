// Configuration constants for the CasparCG Controller client

// API Configuration
export const API_CONFIG = {
  BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api',
  TIMEOUT: 10000, // 10 seconds
};

// CasparCG Media Server Configuration
export const MEDIA_CONFIG = {
  HOST: import.meta.env.VITE_CASPARCG_MEDIA_HOST || '************',
  PORT: import.meta.env.VITE_CASPARCG_MEDIA_PORT || '8000',
  get THUMBNAIL_BASE_URL() {
    return `http://${this.HOST}:${this.PORT}/media/thumbnail`;
  }
};

// UI Configuration
export const UI_CONFIG = {
  TOAST_DURATION: 3000,
  THUMBNAIL_SIZE: {
    WIDTH: 80,
    HEIGHT: 45,
  },
  LARGE_THUMBNAIL_SIZE: {
    WIDTH: 160,
    HEIGHT: 90,
  }
};

// Default channel configuration
export const DEFAULT_CHANNEL_CONFIG = {
  channel1: { channel: 1, layer: 10 },
  channel2: { channel: 2, layer: 10 },
};

// Media types
export const MEDIA_TYPES = {
  VIDEO: 'VIDEO',
  STILL: 'STILL',
  AUDIO: 'AUDIO',
  UNKNOWN: 'UNKNOWN'
};

// Application routes
export const ROUTES = {
  HOME: '/',
  CONFIG: '/config',
  MEDIA_LIST: '/medialist',
  DEV: '/dev'
};
