function PageNotFound() {
  return (
    <kor-page flex-direction="column" style={{ height: "100vh" }}>
      <kor-app-bar slot="top" mobile label="404 Error" />

      <kor-card
        style={{
          flex: "1",
          display: "flex",
          flexDirection: "column",
          overflow: "hidden",
        }}
      >
        <p>Oops! The page you're looking for does not exist.</p>
      </kor-card>
    </kor-page>
  );
}

export default PageNotFound;
