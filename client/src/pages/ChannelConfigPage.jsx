import { useContext, useState } from "react";
import { useNavigate } from "react-router-dom";
import { ChannelConfigContext } from "../ChannelConfigContext";
import { toast } from "react-toastify";

function ChannelConfigPage() {
  const navigate = useNavigate();
  const { channelConfig, setChannelConfig, saveConfig, loading, error } = useContext(ChannelConfigContext);
  const [saving, setSaving] = useState(false);

  if (!channelConfig) return <kor-text>Loading configuration...</kor-text>;

  const handleChange = (channelKey, key, value) => {
    // Convert to number and ensure it's a valid positive integer
    const numValue = parseInt(value, 10);
    const finalValue = isNaN(numValue) || numValue < 1 ? 1 : numValue;

    setChannelConfig((prev) => ({
      ...prev,
      [channelKey]: {
        ...prev[channelKey],
        [key]: finalValue,
      },
    }));
  };

  const handleSave = async () => {
    // Validate configuration before saving
    const isValid = channelConfig &&
      channelConfig.channel1 &&
      channelConfig.channel2 &&
      typeof channelConfig.channel1.channel === 'number' &&
      typeof channelConfig.channel1.layer === 'number' &&
      typeof channelConfig.channel2.channel === 'number' &&
      typeof channelConfig.channel2.layer === 'number' &&
      channelConfig.channel1.channel > 0 &&
      channelConfig.channel1.layer > 0 &&
      channelConfig.channel2.channel > 0 &&
      channelConfig.channel2.layer > 0;

    if (!isValid) {
      toast.error("Invalid configuration. Please ensure all values are positive numbers.");
      return;
    }

    try {
      setSaving(true);
      await saveConfig(channelConfig);
      toast.success("Configuration saved successfully!");
      navigate("/");
    } catch (err) {
      toast.error("Failed to save configuration: " + err.message);
    } finally {
      setSaving(false);
    }
  };

  return (
    <kor-page flex-direction="column" style={{ height: "100vh" }}>
      <kor-app-bar slot="top" mobile label="CasparCG Channel Configuration" />

      <kor-card
        style={{
          flex: "1",
          display: "flex",
          flexDirection: "column",
          overflow: "hidden",
        }}
      >
        {["channel1", "channel2"].map((key, index) => (
          <kor-card key={key} flat>
            <kor-text size="header3">Channel {index + 1}</kor-text>
            <kor-input
              label="Channel"
              type="number"
              min="1"
              max="99"
              value={channelConfig[key].channel}
              onInput={(e) => handleChange(key, "channel", e.target.value)}
              style={{ width: "25%" }}
            ></kor-input>
            <kor-input
              label="Layer"
              type="number"
              min="1"
              max="999"
              value={channelConfig[key].layer}
              onInput={(e) => handleChange(key, "layer", e.target.value)}
              style={{ width: "25%" }}
            ></kor-input>
          </kor-card>
        ))}
        {error && (
          <kor-text color="var(--error-color)" style={{ marginTop: "0.5rem" }}>
            Error: {error}
          </kor-text>
        )}
        <kor-button
          onClick={handleSave}
          label={saving ? "Saving..." : "Save"}
          color="primary"
          disabled={saving}
          style={{ width: "100%", marginTop: "0.5rem" }}
        />
      </kor-card>
    </kor-page>
  );
}

export default ChannelConfigPage;
