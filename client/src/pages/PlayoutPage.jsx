import { useState, useEffect, useContext } from "react";
import { ChannelConfigContext } from "../ChannelConfigContext";
import { useCasparCG } from "../hooks/useApi";
import MediaItem from "../components/MediaItem";
import ChannelControl from "../components/ChannelControl";
import { toast } from "react-toastify";



// Main PlayoutPage Component
function PlayoutPage() {
  const context = useContext(ChannelConfigContext);
  const { channelConfig, loading: configLoading, error: configError } = context || {};

  const [media, setMedia] = useState([]);
  const [playlist1, setPlaylist1] = useState([]);
  const [playlist2, setPlaylist2] = useState([]);
  const [selectedClip1, setSelectedClip1] = useState(null);
  const [selectedClip2, setSelectedClip2] = useState(null);
  const [loopState, setLoopState] = useState({});

  const {
    getMedia,
    play,
    pause,
    clear,
    loading: apiLoading,
    error: apiError
  } = useCasparCG();

  useEffect(() => {
    const fetchMedia = async () => {
      try {
        const mediaData = await getMedia();
        setMedia(mediaData);
      } catch (err) {
        console.error("Failed to fetch media", err);
        toast.error("Failed to fetch media");
      }
    };

    fetchMedia();
  }, [getMedia]);

  if (!channelConfig) {
    return <kor-text>Loading channel configuration...</kor-text>;
  }

  const channel1Config = channelConfig.channel1;
  const channel2Config = channelConfig.channel2;

  // Handler functions
  const handlePlayPlaylist = async (playlist, config, selectedClip) => {
    const clipToPlay = selectedClip || playlist[0];
    if (!clipToPlay) {
      toast.warning("No clip selected or playlist is empty");
      return;
    }

    try {
      await play(
        config.channel,
        config.layer,
        clipToPlay.name,
        loopState[config.channel] ? "LOOP" : ""
      );
    } catch (error) {
      console.error("Play failed:", error);
    }
  };

  const handlePause = async (config) => {
    try {
      await pause(config.channel, config.layer);
    } catch (error) {
      console.error("Pause failed:", error);
    }
  };

  const handleClear = async (config) => {
    try {
      await clear(config.channel, config.layer);
    } catch (error) {
      console.error("Clear failed:", error);
    }
  };

  const toggleLoop = (channel) => {
    setLoopState((prev) => ({
      ...prev,
      [channel]: !prev[channel],
    }));
  };

  // Playlist management functions
  const addToPlaylist1 = (item) => {
    setPlaylist1((prev) =>
      prev.some((clip) => clip.name === item.name) ? prev : [...prev, item]
    );
  };

  const addToPlaylist2 = (item) => {
    setPlaylist2((prev) =>
      prev.some((clip) => clip.name === item.name) ? prev : [...prev, item]
    );
  };



  return (
    <kor-page flex-direction="column" style={{ height: "100vh" }}>
      <kor-app-bar slot="top" mobile label="CasparCG Playout" />
      <kor-grid style={{ marginTop: "0.1rem", marginBottom: "0.1rem" }}>
        <kor-card grid-cols="3">
          {apiLoading ? (
            <kor-text>Loading media...</kor-text>
          ) : media.length === 0 ? (
            <kor-text>No media files found</kor-text>
          ) : (
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                gap: "1rem",
              }}
            >
              {media.map((item, idx) => (
                <MediaItem
                  key={item.name || idx}
                  item={item}
                  onAddToChannel1={addToPlaylist1}
                  onAddToChannel2={addToPlaylist2}
                />
              ))}
            </div>
          )}
        </kor-card>

        <kor-card grid-cols="9">
          <div style={{ flex: "2", paddingLeft: "2rem" }}>
            <div style={{ display: "flex", gap: "2rem" }}>
              {/* Channel 1 */}
              <ChannelControl
                channelNumber={1}
                playlist={playlist1}
                setPlaylist={setPlaylist1}
                selectedClip={selectedClip1}
                setSelectedClip={setSelectedClip1}
                onPlay={() => handlePlayPlaylist(playlist1, channel1Config, selectedClip1)}
                onPause={() => handlePause(channel1Config)}
                onClear={() => handleClear(channel1Config)}
                onToggleLoop={() => toggleLoop(channel1Config.channel)}
                isLooping={loopState[channel1Config.channel] || false}
              />

              {/* Channel 2 */}
              <ChannelControl
                channelNumber={2}
                playlist={playlist2}
                setPlaylist={setPlaylist2}
                selectedClip={selectedClip2}
                setSelectedClip={setSelectedClip2}
                onPlay={() => handlePlayPlaylist(playlist2, channel2Config, selectedClip2)}
                onPause={() => handlePause(channel2Config)}
                onClear={() => handleClear(channel2Config)}
                onToggleLoop={() => toggleLoop(channel2Config.channel)}
                isLooping={loopState[channel2Config.channel] || false}
              />
            </div>
          </div>
        </kor-card>
      </kor-grid>
    </kor-page>
  );
}

export default PlayoutPage;
