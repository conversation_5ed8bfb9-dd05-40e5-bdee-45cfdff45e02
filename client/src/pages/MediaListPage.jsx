import { useState, useEffect } from "react";
import Thumbnail from "../components/Thumbnail";
import LoadingSpinner from "../components/LoadingSpinner";
import { formatFileSize, formatDate } from "../utils/formatters";

function MediaListPage() {
  const [media, setMedia] = useState([]);
  const [loadingMedia, setLoadingMedia] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchMedia = async () => {
      try {
        setLoadingMedia(true);
        setError(null);
        const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api'}/media`);
        const data = await response.json();
        setMedia(data.data || []);
      } catch (err) {
        console.error("Failed to fetch media", err);
        setError(err.message);
      } finally {
        setLoadingMedia(false);
      }
    };

    fetchMedia();
  }, []); // Empty dependency array - only run once on mount

  if (error) {
    return (
      <kor-page flex-direction="column" style={{ height: "100vh" }}>
        <kor-app-bar slot="top" mobile label="Media List" />
        <kor-card style={{ flex: "1", display: "flex", alignItems: "center", justifyContent: "center" }}>
          <kor-text color="var(--error-color)">
            Error loading media: {error}
          </kor-text>
        </kor-card>
      </kor-page>
    );
  }

  return (
    <kor-page flex-direction="column" style={{ height: "100vh" }}>
      <kor-app-bar slot="top" mobile label="Media List" />

      <kor-card
        style={{
          flex: "1",
          display: "flex",
          flexDirection: "column",
          overflow: "hidden",
        }}
      >
        {loadingMedia ? (
          <LoadingSpinner message="Loading media files..." />
        ) : media.length === 0 ? (
          <div style={{ display: "flex", alignItems: "center", justifyContent: "center", flex: "1" }}>
            <kor-text>No media files found</kor-text>
          </div>
        ) : (
          <div style={{ overflowY: "auto", flex: "1", paddingRight: "0.5rem" }}>
            <div style={{ padding: "1rem 0", borderBottom: "1px solid var(--border-color)" }}>
              <kor-text style={{ fontWeight: "bold" }}>
                {media.length} media file{media.length !== 1 ? 's' : ''} found
              </kor-text>
            </div>
            {media.map((item, idx) => (
              <div
                key={item.name || idx}
                style={{
                  display: "flex",
                  gap: "1rem",
                  alignItems: "center",
                  padding: "1rem 0",
                  borderBottom: "1px solid var(--border-color)",
                }}
              >
                <Thumbnail name={item.name} type={item.type} size="large" />
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    gap: "0.25rem",
                    flex: "1",
                  }}
                >
                  <kor-text style={{ fontSize: "1.2rem", fontWeight: "bold" }}>
                    {item.name || 'Unknown'}
                  </kor-text>
                  <kor-text size="body2" color="var(--text-2)">
                    {item.type} • {formatFileSize(item.filesize)} • {formatDate(item.lastModified)}
                  </kor-text>
                  {item.frameCount && item.frameRate && (
                    <kor-text size="body2" color="var(--text-2)">
                      {item.frameCount} frames • {item.frameRate} fps
                    </kor-text>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </kor-card>
    </kor-page>
  );
}

export default MediaListPage;
