// src/ChannelConfigContext.jsx
import { createContext, useState, useEffect, useCallback } from 'react';
import { useApi } from './hooks/useApi';
import { DEFAULT_CHANNEL_CONFIG } from './config/constants';

export const ChannelConfigContext = createContext();

export function ChannelConfigProvider({ children }) {
  const [channelConfig, setChannelConfig] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const api = useApi();

  // Load config from backend on mount
  useEffect(() => {
    const loadConfig = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api'}/config`);
        const data = await response.json();
        setChannelConfig(data.data || DEFAULT_CHANNEL_CONFIG);
      } catch (err) {
        console.error("Failed to load channel config:", err);
        setError(err.message);
        // Fallback to defaults if loading fails
        setChannelConfig(DEFAULT_CHANNEL_CONFIG);
      } finally {
        setLoading(false);
      }
    };

    loadConfig();
  }, []); // Empty dependency array - only run once on mount

  // Save config to backend when it changes
  const saveConfig = useCallback(async (newConfig) => {
    if (!newConfig) return;

    try {
      await api.post('config', newConfig, { showSuccessToast: false });
    } catch (err) {
      console.error("Failed to save config:", err);
      setError(err.message);
    }
  }, [api.post]);

  // Update config function that also saves to backend
  const updateChannelConfig = useCallback((newConfig) => {
    setChannelConfig(newConfig);
    // Debounce the save operation
    const timeoutId = setTimeout(() => {
      saveConfig(newConfig);
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [saveConfig]);

  const contextValue = {
    channelConfig,
    setChannelConfig: updateChannelConfig,
    loading,
    error,
    saveConfig,
  };

  return (
    <ChannelConfigContext.Provider value={contextValue}>
      {children}
    </ChannelConfigContext.Provider>
  );
}