import { BrowserRouter, Routes, Route } from "react-router-dom";
import { ChannelConfigProvider } from "./ChannelConfigContext";
import { ToastContainer } from "react-toastify";
import ErrorBoundary from "./components/ErrorBoundary";
import PlayoutPage from "./pages/PlayoutPage";
import ChannelConfigPage from "./pages/ChannelConfigPage";
import MediaListPage from "./pages/MediaListPage";
import PageNotFound from "./pages/404Page";
import DevPage from "./pages/DevPage";
import { UI_CONFIG } from "./config/constants";
import "react-toastify/dist/ReactToastify.css";

function App() {
  return (
    <ErrorBoundary>
      <ChannelConfigProvider>
        <BrowserRouter>
          <ToastContainer
            position="top-right"
            autoClose={UI_CONFIG.TOAST_DURATION}
            hideProgressBar={false}
            newestOnTop
            closeOnClick
            pauseOnFocusLoss
            draggable
            pauseOnHover
            theme="colored"
            limit={5}
          />
          <Routes>
            <Route path="/" element={<PlayoutPage />} />
            <Route path="/config" element={<ChannelConfigPage />} />
            <Route path="/medialist" element={<MediaListPage />} />
            <Route path="/dev" element={<DevPage />} />
            <Route path="*" element={<PageNotFound />} />
          </Routes>
        </BrowserRouter>
      </ChannelConfigProvider>
    </ErrorBoundary>
  );
}

export default App;
