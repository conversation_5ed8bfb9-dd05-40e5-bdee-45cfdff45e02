import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

const PlaylistItem = ({ clip, id, isSelected, onSelect, onDelete }) => {
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    padding: "0.75rem 1rem",
    border: isSelected ? "2px solid #007bff" : "1px solid #ccc",
    borderRadius: "5px",
    background: isSelected ? "#e6f0ff" : "#fff",
    cursor: "pointer",
  };

  return (
    <div ref={setNodeRef} style={style} onClick={() => onSelect(clip)}>
      <kor-card flat flex-direction="row">
        <kor-text size="header-2">{clip.name}</kor-text>
        <kor-icon
          icon="delete"
          button
          onClick={(e) => {
            e.stopPropagation();
            onDelete(clip);
          }}
        ></kor-icon>
        <kor-icon
          icon="drag_indicator"
          button
          {...attributes}
          {...listeners}
          style={{ cursor: "grab" }}
        ></kor-icon>
      </kor-card>
    </div>
  );
};

export default PlaylistItem;
