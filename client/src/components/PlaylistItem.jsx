import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { RotateCcw } from "lucide-react";

const PlaylistItem = ({ clip, id, isSelected, onSelect, onDelete, isPlaying = false, isLooping = false }) => {
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    padding: "0.75rem 1rem",
    border: isSelected ? "2px solid #007bff" : isPlaying ? "2px solid #28a745" : "1px solid #ccc",
    borderRadius: "5px",
    background: isSelected ? "#e6f0ff" : isPlaying ? "#f0fff4" : "#fff",
    cursor: "pointer",
  };

  return (
    <div ref={setNodeRef} style={style} onClick={() => onSelect(clip)}>
      <kor-card flat flex-direction="row">
        <div style={{ display: "flex", alignItems: "center", gap: "0.5rem", flex: 1 }}>
          <kor-text size="header-2">{clip.name}</kor-text>
          {isPlaying && (
            <div style={{
              display: "flex",
              alignItems: "center",
              gap: "0.25rem",
              padding: "0.125rem 0.375rem",
              backgroundColor: "#28a745",
              color: "white",
              borderRadius: "3px",
              fontSize: "0.625rem",
              fontWeight: "bold"
            }}>
              ▶ PLAYING
            </div>
          )}
          {isPlaying && isLooping && (
            <div style={{
              display: "flex",
              alignItems: "center",
              gap: "0.25rem",
              padding: "0.125rem 0.375rem",
              backgroundColor: "#007bff",
              color: "white",
              borderRadius: "3px",
              fontSize: "0.625rem",
              fontWeight: "bold"
            }}>
              <RotateCcw size={10} />
              LOOP
            </div>
          )}
        </div>
        <kor-icon
          icon="delete"
          button
          onClick={(e) => {
            e.stopPropagation();
            onDelete(clip);
          }}
        ></kor-icon>
        <kor-icon
          icon="drag_indicator"
          button
          {...attributes}
          {...listeners}
          style={{ cursor: "grab" }}
        ></kor-icon>
      </kor-card>
    </div>
  );
};

export default PlaylistItem;
