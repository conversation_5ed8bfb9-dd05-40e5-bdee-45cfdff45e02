import Thumbnail from './Thumbnail';
import { formatDuration } from '../utils/formatters';

const MediaItem = ({ item, onAddToChannel1, onAddToChannel2 }) => {
  if (!item) return null;

  return (
    <div
      style={{
        border: "1px solid #ddd",
        padding: "1rem",
        borderRadius: "8px",
        display: "flex",
        alignItems: "center",
        gap: "1rem",
      }}
    >
      <Thumbnail name={item.name} type={item.type} />
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "0.25rem",
          flex: "1",
        }}
      >
        <kor-text style={{ fontWeight: "bold" }}>
          {item.name}
        </kor-text>
        <kor-text>
          {item.type}
          {item.frameCount && item.frameRate
            ? ` • ${formatDuration(item.frameCount, item.frameRate)}`
            : ""}
        </kor-text>
        <div
          style={{
            display: "flex",
            gap: "0.5rem",
            marginTop: "0.5rem",
          }}
        >
          <kor-button
            label="Add to Ch1"
            onClick={() => onAddToChannel1(item)}
          />
          <kor-button
            label="Add to Ch2"
            onClick={() => onAddToChannel2(item)}
          />
        </div>
      </div>
    </div>
  );
};

export default MediaItem;
