import { Component } from 'react';

class ErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Log the error to console or error reporting service
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    this.setState({
      error: error,
      errorInfo: errorInfo
    });
  }

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      return (
        <kor-page flex-direction="column" style={{ height: "100vh" }}>
          <kor-app-bar slot="top" mobile label="Error" />
          <kor-card style={{ 
            flex: "1", 
            display: "flex", 
            flexDirection: "column",
            alignItems: "center", 
            justifyContent: "center",
            padding: "2rem",
            textAlign: "center"
          }}>
            <kor-text size="header-1" style={{ marginBottom: "1rem", color: "var(--error-color)" }}>
              Something went wrong
            </kor-text>
            <kor-text style={{ marginBottom: "2rem" }}>
              An unexpected error occurred. Please refresh the page or contact support if the problem persists.
            </kor-text>
            
            {process.env.NODE_ENV === 'development' && (
              <details style={{ marginTop: "1rem", textAlign: "left", width: "100%" }}>
                <summary style={{ cursor: "pointer", marginBottom: "1rem" }}>
                  <kor-text>Error Details (Development)</kor-text>
                </summary>
                <pre style={{ 
                  background: "#f5f5f5", 
                  padding: "1rem", 
                  borderRadius: "4px",
                  overflow: "auto",
                  fontSize: "12px"
                }}>
                  {this.state.error && this.state.error.toString()}
                  <br />
                  {this.state.errorInfo.componentStack}
                </pre>
              </details>
            )}
            
            <kor-button 
              label="Refresh Page" 
              color="Primary"
              onClick={() => window.location.reload()}
              style={{ marginTop: "1rem" }}
            />
          </kor-card>
        </kor-page>
      );
    }

    // If no error, render children normally
    return this.props.children;
  }
}

export default ErrorBoundary;
