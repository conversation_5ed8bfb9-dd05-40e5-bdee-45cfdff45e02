import { useState } from 'react';
import { ImageOff } from 'lucide-react';
import { MEDIA_CONFIG, UI_CONFIG, MEDIA_TYPES } from '../config/constants';

const Thumbnail = ({ 
  name, 
  type = MEDIA_TYPES.UNKNOWN, 
  size = 'small',
  className = '',
  style = {},
  showFallback = true 
}) => {
  const [imageError, setImageError] = useState(false);
  const [imageLoading, setImageLoading] = useState(true);

  const sizeConfig = size === 'large' 
    ? UI_CONFIG.LARGE_THUMBNAIL_SIZE 
    : UI_CONFIG.THUMBNAIL_SIZE;

  const containerStyle = {
    width: `${sizeConfig.WIDTH}px`,
    height: `${sizeConfig.HEIGHT}px`,
    backgroundColor: '#eee',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: '4px',
    overflow: 'hidden',
    position: 'relative',
    ...style,
  };

  const handleImageError = () => {
    setImageError(true);
    setImageLoading(false);
  };

  const handleImageLoad = () => {
    setImageLoading(false);
  };

  // Show fallback if no name provided or image failed to load
  if (!name || imageError) {
    return (
      <div className={`thumbnail-fallback ${className}`} style={containerStyle}>
        {showFallback ? (
          <>
            <ImageOff size={size === 'large' ? 24 : 16} color="#666" />
            <span 
              style={{ 
                fontSize: size === 'large' ? '14px' : '10px',
                color: '#666',
                marginLeft: '4px',
                textTransform: 'uppercase'
              }}
            >
              {type}
            </span>
          </>
        ) : (
          <div style={{ fontSize: '12px', color: '#666' }}>
            {type}
          </div>
        )}
      </div>
    );
  }

  const thumbnailUrl = `${MEDIA_CONFIG.THUMBNAIL_BASE_URL}/${encodeURIComponent(name)}`;

  return (
    <div className={`thumbnail-container ${className}`} style={containerStyle}>
      {imageLoading && (
        <div 
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: '#f0f0f0',
            fontSize: '12px',
            color: '#666'
          }}
        >
          Loading...
        </div>
      )}
      <img
        src={thumbnailUrl}
        alt={name}
        style={{ 
          width: '100%', 
          height: '100%', 
          objectFit: 'cover',
          display: imageLoading ? 'none' : 'block'
        }}
        onError={handleImageError}
        onLoad={handleImageLoad}
        loading="lazy"
      />
    </div>
  );
};

export default Thumbnail;
