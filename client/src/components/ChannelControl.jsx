import { <PERSON>Off, RotateCcw } from "lucide-react";
import { DndContext, closestCenter } from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import PlaylistItem from './PlaylistItem';
import { toast } from "react-toastify";

const ChannelControl = ({
  channelNumber,
  playlist,
  setPlaylist,
  selectedClip,
  setSelectedClip,
  onPlay,
  onPause,
  onClear,
  onToggleLoop,
  isLooping = false,
  currentlyPlaying = null, // Name of the currently playing clip
}) => {
  const handleDeleteClip = (clip) => {
    setPlaylist((prev) => prev.filter((c) => c.name !== clip.name));
  };

  const handleClearPlaylist = () => {
    setPlaylist([]);
    toast.info(`Cleared Channel ${channelNumber} Playlist`);
  };

  const handleDragEnd = ({ active, over }) => {
    if (!over || active.id === over.id) return;
    
    const oldIndex = playlist.findIndex((item) => item.name === active.id);
    const newIndex = playlist.findIndex((item) => item.name === over.id);
    
    if (oldIndex !== -1 && newIndex !== -1) {
      setPlaylist((items) => arrayMove(items, oldIndex, newIndex));
    }
  };

  return (
    <div style={{ flex: 1 }}>
      <div style={{
        display: "flex",
        alignItems: "center",
        gap: "0.5rem",
        marginBottom: "1rem"
      }}>
        <h2 style={{ margin: 0 }}>Channel {channelNumber}</h2>
        {isLooping && (
          <div style={{
            display: "flex",
            alignItems: "center",
            gap: "0.25rem",
            padding: "0.25rem 0.5rem",
            backgroundColor: "#007bff",
            color: "white",
            borderRadius: "4px",
            fontSize: "0.75rem",
            fontWeight: "bold"
          }}>
            <RotateCcw size={14} />
            LOOP
          </div>
        )}
      </div>
      
      {/* Video Preview Area */}
      <div
        style={{
          width: "100%",
          aspectRatio: "16/9",
          backgroundColor: "#ccc",
          borderRadius: "8px",
          marginBottom: "1rem",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <ImageOff />
      </div>

      {/* Control Buttons */}
      <kor-card flat flex-direction="row">
        <kor-button
          label="Play"
          color="Primary"
          onClick={onPlay}
        />
        <kor-button
          label="Pause"
          color="Primary"
          onClick={onPause}
        />
        <kor-button
          label="Clear Channel"
          color="Primary"
          onClick={onClear}
        />
        <kor-button
          label="Clear Playlist"
          color="Primary"
          onClick={handleClearPlaylist}
        />
        <kor-button
          label={isLooping ? "🔄 Loop ON" : "Loop"}
          color={isLooping ? "Secondary" : "Primary"}
          onClick={onToggleLoop}
          toggled={isLooping}
        />
      </kor-card>

      {/* Playlist */}
      <h3>Playlist</h3>
      {playlist.length === 0 ? (
        <p>No clips.</p>
      ) : (
        <DndContext
          collisionDetection={closestCenter}
          onDragEnd={handleDragEnd}
        >
          <SortableContext
            items={playlist.map((item) => item.name)}
            strategy={verticalListSortingStrategy}
          >
            <div style={{ display: "flex", flexDirection: "column", gap: "1rem" }}>
              {playlist.map((clip) => (
                <PlaylistItem
                  key={clip.name}
                  id={clip.name}
                  clip={clip}
                  isSelected={selectedClip?.name === clip.name}
                  isPlaying={currentlyPlaying === clip.name}
                  isLooping={isLooping && currentlyPlaying === clip.name}
                  onSelect={setSelectedClip}
                  onDelete={handleDeleteClip}
                />
              ))}
            </div>
          </SortableContext>
        </DndContext>
      )}
    </div>
  );
};

export default ChannelControl;
