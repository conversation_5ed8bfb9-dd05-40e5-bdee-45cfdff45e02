{"name": "casparcg-controller-server", "version": "1.0.0", "description": "CasparCG Controller Server - Backend API for controlling CasparCG Server", "main": "index.js", "type": "module", "scripts": {"start": "node index.js", "dev": "node --watch index.js", "test": "echo \"Error: no test specified\" && exit 1", "lint": "echo \"No linting configured\"", "health": "curl -f http://localhost:3001/api/health || exit 1"}, "keywords": ["casparcg", "broadcast", "video", "controller"], "author": "", "license": "ISC", "dependencies": {"casparcg-connection": "^6.3.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0"}}