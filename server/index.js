// server/index.js
import express from "express";
import cors from "cors";
import { CasparCG } from "casparcg-connection";
import fs from 'fs/promises';
import dotenv from 'dotenv';
import {
  validateChannelLayer,
  validatePlayRequest,
  validateChannelConfig
} from './utils/validation.js';
import logger from './utils/logger.js';
import { requestTimer, startMemoryMonitoring } from './middleware/performance.js';

// Load environment variables
dotenv.config();

// Configuration
const config = {
  port: process.env.PORT || 3001,
  casparHost: process.env.CASPARCG_HOST || "************",
  casparPort: parseInt(process.env.CASPARCG_PORT) || 5250,
  mediaHost: process.env.CASPARCG_MEDIA_HOST || "************",
  mediaPort: parseInt(process.env.CASPARCG_MEDIA_PORT) || 8000,
  configPath: process.env.CONFIG_PATH || './channel-config.json',
  corsOrigin: process.env.CORS_ORIGIN || "http://localhost:5173"
};

const app = express();

// Middleware
app.use(cors({
  origin: config.corsOrigin,
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(requestTimer);

// Create CasparCG connection
const caspar = new CasparCG({
  host: config.casparHost,
  port: config.casparPort,
});

// Connection status listeners
caspar.on("connect", () => {
  logger.connectionEvent('connect', {
    host: config.casparHost,
    port: config.casparPort
  });
});

caspar.on("error", (err) => {
  logger.connectionEvent('error', {
    error: err.message || err,
    host: config.casparHost,
    port: config.casparPort
  });
});

caspar.on("disconnect", () => {
  logger.connectionEvent('disconnect', {
    host: config.casparHost,
    port: config.casparPort
  });
});

// Utility functions
const handleApiError = (error, operation, res) => {
  console.error(`❌ ${operation} API error:`, error);
  res.status(500).json({
    success: false,
    error: error.message || 'Internal server error',
    operation
  });
};

const validateChannelLayer = (channel, layer) => {
  if (!channel || !layer) {
    return { valid: false, error: 'Channel and layer are required' };
  }
  if (!Number.isInteger(channel) || !Number.isInteger(layer)) {
    return { valid: false, error: 'Channel and layer must be integers' };
  }
  if (channel < 1 || layer < 1) {
    return { valid: false, error: 'Channel and layer must be positive integers' };
  }
  return { valid: true };
};

// ---- API ROUTES ----

// PLAY
app.post("/api/play", async (req, res) => {
  // Validate and sanitize input
  const validation = validatePlayRequest(req.body);
  if (!validation.valid) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: validation.errors
    });
  }

  const { channel, layer, clip, params } = validation.data;

  try {
    const playParams = { channel, layer, clip };
    if (params) playParams.params = params;

    const { error } = await caspar.play(playParams);
    if (error) {
      console.error("❌ CasparCG play error:", error);
      return res.status(500).json({ success: false, error: error.message || error });
    }

    res.json({
      success: true,
      message: `Playing ${clip} on channel ${channel}, layer ${layer}${params ? ` (${params})` : ''}`,
      data: { channel, layer, clip, params }
    });
  } catch (error) {
    handleApiError(error, 'Play', res);
  }
});

// PAUSE
app.post("/api/pause", async (req, res) => {
  const { channel, layer } = req.body;

  const validation = validateChannelLayer(channel, layer);
  if (!validation.valid) {
    return res.status(400).json({ success: false, error: validation.error });
  }

  try {
    const { error, request } = await caspar.pause({ channel, layer });
    if (error) {
      console.error("❌ CasparCG pause error:", error);
      return res.status(500).json({ success: false, error: error.message || error });
    }

    const response = await request;
    res.json({
      success: true,
      message: `Paused channel ${channel}, layer ${layer}`,
      data: { channel, layer },
      response
    });
  } catch (error) {
    handleApiError(error, 'Pause', res);
  }
});

// STOP
app.post("/api/stop", async (req, res) => {
  const { channel, layer } = req.body;

  const validation = validateChannelLayer(channel, layer);
  if (!validation.valid) {
    return res.status(400).json({ success: false, error: validation.error });
  }

  try {
    const { error } = await caspar.stop({ channel, layer });
    if (error) {
      console.error("❌ CasparCG stop error:", error);
      return res.status(500).json({ success: false, error: error.message || error });
    }

    res.json({
      success: true,
      message: `Stopped channel ${channel}, layer ${layer}`,
      data: { channel, layer }
    });
  } catch (error) {
    handleApiError(error, 'Stop', res);
  }
});

// CLEAR
app.post("/api/clear", async (req, res) => {
  const { channel, layer } = req.body;

  const validation = validateChannelLayer(channel, layer);
  if (!validation.valid) {
    return res.status(400).json({ success: false, error: validation.error });
  }

  try {
    const { error } = await caspar.clear({ channel, layer });
    if (error) {
      console.error("❌ CasparCG clear error:", error);
      return res.status(500).json({ success: false, error: error.message || error });
    }

    res.json({
      success: true,
      message: `Cleared channel ${channel}, layer ${layer}`,
      data: { channel, layer }
    });
  } catch (error) {
    handleApiError(error, 'Clear', res);
  }
});

// INFO
app.post("/api/info", async (req, res) => {
  const { channel, layer } = req.body;

  const validation = validateChannelLayer(channel, layer);
  if (!validation.valid) {
    return res.status(400).json({ success: false, error: validation.error });
  }

  try {
    const { response, error } = await caspar.info({ channel, layer });
    if (error) {
      console.error("❌ CasparCG info error:", error);
      return res.status(500).json({ success: false, error: error.message || error });
    }

    res.json({
      success: true,
      message: `Info retrieved for channel ${channel}, layer ${layer}`,
      data: response
    });
  } catch (error) {
    handleApiError(error, 'Info', res);
  }
});

// MEDIA LIST (CLS Command)
app.get("/api/media", async (req, res) => {
  try {
    const { request, error } = await caspar.cls();
    if (error) {
      console.error("❌ CasparCG CLS error:", error);
      return res.status(500).json({
        success: false,
        error: error.message || error,
        operation: 'media_list'
      });
    }

    const result = await request;
    const files = result.data;

    if (!Array.isArray(files)) {
      console.error("❌ CLS: Expected an array, but got", typeof files);
      return res.status(500).json({
        success: false,
        error: "Invalid response format from CasparCG server",
        operation: 'media_list'
      });
    }

    // Transform and validate media data
    const media = files.map(item => {
      const { clip, type, size, datetime, frames, framerate } = item;

      return {
        name: clip || 'Unknown',
        type: type || 'UNKNOWN',
        filesize: size || 0,
        lastModified: datetime ? new Date(datetime).toISOString() : null,
        frameCount: frames || 0,
        frameRate: framerate ? String(framerate) : '0',
        thumbnailUrl: `http://${config.mediaHost}:${config.mediaPort}/media/thumbnail/${clip}`
      };
    });

    res.json({
      success: true,
      message: `Retrieved ${media.length} media files`,
      data: media,
      count: media.length
    });
  } catch (error) {
    handleApiError(error, 'Media List', res);
  }
});

// CONFIG ROUTES
app.get("/api/config", async (req, res) => {
  try {
    const configData = await fs.readFile(config.configPath, 'utf8');
    const channelConfig = JSON.parse(configData);

    res.json({
      success: true,
      message: 'Configuration retrieved successfully',
      data: channelConfig
    });
  } catch (error) {
    console.error("❌ Failed to read config:", error);

    // Return default config if file doesn't exist
    const defaultConfig = {
      channel1: { channel: 1, layer: 10 },
      channel2: { channel: 2, layer: 10 }
    };

    res.json({
      success: true,
      message: 'Using default configuration',
      data: defaultConfig,
      isDefault: true
    });
  }
});

app.post('/api/config', async (req, res) => {
  try {
    const newConfig = req.body;

    // Validate configuration
    const validation = validateChannelConfig(newConfig);
    if (!validation.valid) {
      return res.status(400).json({
        success: false,
        error: 'Invalid configuration data',
        details: validation.errors
      });
    }

    await fs.writeFile(config.configPath, JSON.stringify(newConfig, null, 2));

    res.json({
      success: true,
      message: 'Configuration saved successfully',
      data: newConfig
    });
  } catch (error) {
    handleApiError(error, 'Save Config', res);
  }
});

// Health check route
app.get("/api/health", (req, res) => {
  res.json({
    success: true,
    message: "CasparCG Controller Server is running",
    timestamp: new Date().toISOString(),
    config: {
      casparHost: config.casparHost,
      casparPort: config.casparPort,
      mediaHost: config.mediaHost,
      mediaPort: config.mediaPort
    }
  });
});

// Root route
app.get("/", (req, res) => {
  res.send(`
    <h1>CasparCG Controller Server</h1>
    <p>Server is running on port ${config.port}</p>
    <p>Connected to CasparCG at ${config.casparHost}:${config.casparPort}</p>
    <p><a href="/api/health">Health Check</a></p>
  `);
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('❌ Unhandled error:', error);
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    message: error.message
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found',
    path: req.path
  });
});

// ---- START SERVER ----
app.listen(config.port, () => {
  logger.info(`🚀 CasparCG Controller Server started`, {
    port: config.port,
    url: `http://localhost:${config.port}`,
    casparServer: `${config.casparHost}:${config.casparPort}`,
    mediaServer: `${config.mediaHost}:${config.mediaPort}`,
    environment: process.env.NODE_ENV || 'development'
  });

  // Start memory monitoring in development
  if (process.env.NODE_ENV === 'development') {
    startMemoryMonitoring(60000); // Monitor every minute
  }
});