// Performance monitoring middleware

import logger from '../utils/logger.js';

/**
 * Request timing middleware
 * Logs request duration and basic performance metrics
 */
export const requestTimer = (req, res, next) => {
  const startTime = Date.now();
  
  // Store original end function
  const originalEnd = res.end;
  
  // Override end function to capture timing
  res.end = function(...args) {
    const duration = Date.now() - startTime;
    
    // Log API requests (skip static files and health checks)
    if (req.path.startsWith('/api') && req.path !== '/api/health') {
      logger.apiRequest(req.method, req.path, res.statusCode, duration);
      
      // Log slow requests
      if (duration > 1000) {
        logger.warn(`Slow request detected`, {
          method: req.method,
          path: req.path,
          duration: `${duration}ms`,
          statusCode: res.statusCode
        });
      }
    }
    
    // Call original end function
    originalEnd.apply(this, args);
  };
  
  next();
};

/**
 * Memory usage monitoring
 * Logs memory usage periodically
 */
export const startMemoryMonitoring = (intervalMs = 60000) => {
  setInterval(() => {
    const memUsage = process.memoryUsage();
    const memUsageMB = {
      rss: Math.round(memUsage.rss / 1024 / 1024),
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
      external: Math.round(memUsage.external / 1024 / 1024)
    };
    
    logger.debug('Memory usage', memUsageMB);
    
    // Warn if memory usage is high
    if (memUsageMB.heapUsed > 100) {
      logger.warn('High memory usage detected', memUsageMB);
    }
  }, intervalMs);
};

/**
 * Error rate monitoring
 * Tracks error rates and logs warnings
 */
class ErrorRateMonitor {
  constructor(windowMs = 60000, threshold = 10) {
    this.windowMs = windowMs;
    this.threshold = threshold;
    this.errors = [];
  }
  
  recordError(error, context = {}) {
    const now = Date.now();
    this.errors.push({ timestamp: now, error, context });
    
    // Clean old errors outside the window
    this.errors = this.errors.filter(e => now - e.timestamp < this.windowMs);
    
    // Check if error rate exceeds threshold
    if (this.errors.length >= this.threshold) {
      logger.warn(`High error rate detected: ${this.errors.length} errors in ${this.windowMs}ms`, {
        errorCount: this.errors.length,
        timeWindow: this.windowMs,
        threshold: this.threshold
      });
    }
  }
  
  getErrorRate() {
    const now = Date.now();
    const recentErrors = this.errors.filter(e => now - e.timestamp < this.windowMs);
    return recentErrors.length;
  }
}

export const errorRateMonitor = new ErrorRateMonitor();
