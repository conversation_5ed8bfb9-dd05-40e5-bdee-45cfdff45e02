// Simple logging utility for CasparCG Controller Server

const LOG_LEVELS = {
  ERROR: 0,
  WARN: 1,
  INFO: 2,
  DEBUG: 3
};

const LOG_COLORS = {
  ERROR: '\x1b[31m', // Red
  WARN: '\x1b[33m',  // Yellow
  INFO: '\x1b[36m',  // Cyan
  DEBUG: '\x1b[37m', // White
  RESET: '\x1b[0m'   // Reset
};

class Logger {
  constructor(level = 'INFO') {
    this.level = LOG_LEVELS[level.toUpperCase()] || LOG_LEVELS.INFO;
  }

  formatMessage(level, message, meta = {}) {
    const timestamp = new Date().toISOString();
    const color = LOG_COLORS[level];
    const reset = LOG_COLORS.RESET;
    
    let formattedMessage = `${color}[${timestamp}] ${level}:${reset} ${message}`;
    
    if (Object.keys(meta).length > 0) {
      formattedMessage += ` ${JSON.stringify(meta)}`;
    }
    
    return formattedMessage;
  }

  error(message, meta = {}) {
    if (this.level >= LOG_LEVELS.ERROR) {
      console.error(this.formatMessage('ERROR', message, meta));
    }
  }

  warn(message, meta = {}) {
    if (this.level >= LOG_LEVELS.WARN) {
      console.warn(this.formatMessage('WARN', message, meta));
    }
  }

  info(message, meta = {}) {
    if (this.level >= LOG_LEVELS.INFO) {
      console.log(this.formatMessage('INFO', message, meta));
    }
  }

  debug(message, meta = {}) {
    if (this.level >= LOG_LEVELS.DEBUG) {
      console.log(this.formatMessage('DEBUG', message, meta));
    }
  }

  // Convenience methods for common operations
  apiRequest(method, endpoint, status, duration) {
    this.info(`${method} ${endpoint} - ${status}`, { duration: `${duration}ms` });
  }

  casparCommand(command, channel, layer, result) {
    const meta = { channel, layer };
    if (result.success) {
      this.info(`CasparCG ${command} successful`, meta);
    } else {
      this.error(`CasparCG ${command} failed: ${result.error}`, meta);
    }
  }

  connectionEvent(event, details = {}) {
    switch (event) {
      case 'connect':
        this.info('✅ Connected to CasparCG Server', details);
        break;
      case 'disconnect':
        this.warn('⚠️ Disconnected from CasparCG Server', details);
        break;
      case 'error':
        this.error('❌ CasparCG Connection error', details);
        break;
      default:
        this.info(`CasparCG ${event}`, details);
    }
  }
}

// Create default logger instance
const logger = new Logger(process.env.LOG_LEVEL || 'INFO');

export default logger;
export { Logger };
