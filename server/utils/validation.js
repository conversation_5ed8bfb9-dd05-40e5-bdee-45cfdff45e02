// Validation utilities for CasparCG Controller Server

/**
 * Validate channel and layer parameters
 * @param {number} channel - Channel number
 * @param {number} layer - Layer number
 * @returns {Object} Validation result
 */
export const validateChannelLayer = (channel, layer) => {
  const errors = [];

  // Check if values are provided
  if (channel === undefined || channel === null) {
    errors.push('Channel is required');
  }
  if (layer === undefined || layer === null) {
    errors.push('Layer is required');
  }

  // Convert to numbers if they're strings
  const channelNum = Number(channel);
  const layerNum = Number(layer);

  // Check if they're valid numbers
  if (isNaN(channelNum)) {
    errors.push('Channel must be a valid number');
  }
  if (isNaN(layerNum)) {
    errors.push('Layer must be a valid number');
  }

  // Check if they're integers
  if (!Number.isInteger(channelNum)) {
    errors.push('Channel must be an integer');
  }
  if (!Number.isInteger(layerNum)) {
    errors.push('Layer must be an integer');
  }

  // Check if they're positive
  if (channelNum < 1) {
    errors.push('Channel must be a positive integer (1 or greater)');
  }
  if (layerNum < 1) {
    errors.push('Layer must be a positive integer (1 or greater)');
  }

  // Check reasonable limits
  if (channelNum > 99) {
    errors.push('Channel must be 99 or less');
  }
  if (layerNum > 999) {
    errors.push('Layer must be 999 or less');
  }

  return {
    valid: errors.length === 0,
    errors,
    channel: channelNum,
    layer: layerNum
  };
};

/**
 * Validate clip name
 * @param {string} clip - Clip name
 * @returns {Object} Validation result
 */
export const validateClipName = (clip) => {
  const errors = [];

  if (!clip || typeof clip !== 'string') {
    errors.push('Clip name is required and must be a string');
  } else {
    // Trim whitespace
    const trimmedClip = clip.trim();
    
    if (trimmedClip.length === 0) {
      errors.push('Clip name cannot be empty');
    }
    
    if (trimmedClip.length > 255) {
      errors.push('Clip name must be 255 characters or less');
    }
    
    // Check for invalid characters (basic check)
    const invalidChars = /[<>:"|?*]/;
    if (invalidChars.test(trimmedClip)) {
      errors.push('Clip name contains invalid characters');
    }
  }

  return {
    valid: errors.length === 0,
    errors,
    clip: clip ? clip.trim() : ''
  };
};

/**
 * Validate channel configuration object
 * @param {Object} config - Channel configuration
 * @returns {Object} Validation result
 */
export const validateChannelConfig = (config) => {
  const errors = [];

  if (!config || typeof config !== 'object') {
    errors.push('Configuration must be an object');
    return { valid: false, errors };
  }

  // Check for required channel configurations
  const requiredChannels = ['channel1', 'channel2'];
  
  for (const channelKey of requiredChannels) {
    if (!config[channelKey]) {
      errors.push(`Missing configuration for ${channelKey}`);
      continue;
    }

    const channelConfig = config[channelKey];
    
    if (typeof channelConfig !== 'object') {
      errors.push(`${channelKey} configuration must be an object`);
      continue;
    }

    // Validate channel and layer for each channel config
    const validation = validateChannelLayer(channelConfig.channel, channelConfig.layer);
    if (!validation.valid) {
      errors.push(`${channelKey}: ${validation.errors.join(', ')}`);
    }
  }

  return {
    valid: errors.length === 0,
    errors
  };
};

/**
 * Sanitize and validate request body for play command
 * @param {Object} body - Request body
 * @returns {Object} Validation result with sanitized data
 */
export const validatePlayRequest = (body) => {
  const { channel, layer, clip, params } = body;
  
  const channelLayerValidation = validateChannelLayer(channel, layer);
  const clipValidation = validateClipName(clip);
  
  const errors = [
    ...channelLayerValidation.errors,
    ...clipValidation.errors
  ];

  // Validate params if provided
  let sanitizedParams = '';
  if (params && typeof params === 'string') {
    const allowedParams = ['LOOP', 'AUTO'];
    const paramList = params.toUpperCase().split(' ').filter(p => p.length > 0);
    const validParams = paramList.filter(p => allowedParams.includes(p));
    sanitizedParams = validParams.join(' ');
  }

  return {
    valid: errors.length === 0,
    errors,
    data: {
      channel: channelLayerValidation.channel,
      layer: channelLayerValidation.layer,
      clip: clipValidation.clip,
      params: sanitizedParams
    }
  };
};
